<template>
  <view class="container">
    <view class="page-header">
      <text class="page-title">联系外运信息</text>
      <text class="page-subtitle" v-if="originalOrder">订单号: {{ originalOrder.id }}</text>
    </view>

    <view class="form-content">
      <!-- 固废信息 -->
      <view class="form-section">
        <view class="section-title">
          <text class="title-text">固废信息</text>
        </view>

        <view class="form-item">
          <text class="label required">固废名称</text>
          <view class="picker" @tap="openWasteNamePicker" @click="openWasteNamePicker">
            <text class="picker-text">{{ formData.wasteName || '请选择固废名称' }}</text>
            <text class="arrow">▼</text>
          </view>
        </view>

        <view class="form-item">
          <text class="label required">包装方式</text>
          <picker class="picker" @change="onPackagingMethodChange" :value="formData.packagingMethodIndex" :range="packagingOptions">
            <text class="picker-text">{{ formData.packagingMethod || '请选择包装方式' }}</text>
            <text class="arrow">▼</text>
          </picker>
        </view>

        <view class="form-item">
          <text class="label required">禁忌与应急措施</text>
          <view class="textarea-wrapper">
            <textarea class="textarea" :value="formData.emergencyMeasures" @input="inputChange($event, 'emergencyMeasures')" placeholder="请输入禁忌与应急措施" />
          </view>
        </view>
      </view>

      <!-- 外运信息 -->
      <view class="form-section">
        <view class="section-title">
          <text class="title-text">外运信息</text>
        </view>

        <view class="form-grid">
          <view class="form-item grid-item">
            <text class="label required">外运目的</text>
            <picker class="picker" @change="onTransportPurposeChange" :value="formData.transportPurposeIndex" :range="transportPurposeOptions">
              <text class="picker-text">{{ formData.transportPurpose || '请选择' }}</text>
              <text class="arrow">▼</text>
            </picker>
          </view>

          <view class="form-item grid-item">
            <text class="label required">废物处置方式</text>
            <picker class="picker" @change="onWasteDisposalMethodChange" :value="formData.wasteDisposalMethodIndex" :range="wasteDisposalMethodOptions">
              <text class="picker-text">{{ formData.wasteDisposalMethod || '请选择' }}</text>
              <text class="arrow">▼</text>
            </picker>
          </view>
        </view>

        <view class="form-item">
          <text class="label required">产生企业</text>
          <picker class="picker" @change="onProductionEnterpriseChange" :value="formData.productionEnterpriseIndex" :range="productionEnterpriseOptions" range-key="label">
            <text class="picker-text">{{ formData.productionEnterprise || '请选择产生企业' }}</text>
            <text class="arrow">▼</text>
          </picker>
        </view>

        <view class="form-item">
          <text class="label required">运输企业</text>
          <picker class="picker" @change="onTransportEnterpriseChange" :value="formData.transportEnterpriseIndex" :range="transportEnterpriseOptions" range-key="label">
            <text class="picker-text">{{ formData.transportEnterprise || '请选择运输企业' }}</text>
            <text class="arrow">▼</text>
          </picker>
        </view>

        <view class="form-item">
          <text class="label required">接收单位</text>
          <picker class="picker" @change="onReceivingUnitChange" :value="formData.receivingUnitIndex" :range="receivingUnitOptions" range-key="label">
            <text class="picker-text">{{ formData.receivingUnit || '请选择接收单位' }}</text>
            <text class="arrow">▼</text>
          </picker>
        </view>

        <view class="form-item">
          <text class="label">五联单号</text>
          <view class="input-wrapper special-input">
            <input class="input" :value="formData.fiveLinkNumber" disabled />
            <view class="auto-generated-tag">自动生成</view>
          </view>
        </view>

        <view class="divider"></view>

        <view class="form-item">
          <text class="label">承运人</text>
          <view class="input-wrapper">
            <input class="input" :value="formData.carrier" @input="inputChange($event, 'carrier')" placeholder="请输入承运人" />
            <view class="input-icon person-icon"></view>
          </view>
        </view>

        <view class="form-grid">
          <view class="form-item grid-item">
            <text class="label required">运输车牌</text>
            <picker class="picker" @change="onTransportVehicleChange" :value="formData.transportVehicleIndex" :range="transportVehicleOptions" range-key="label">
              <text class="picker-text">{{ formData.transportVehicle || '请选择' }}</text>
              <text class="arrow">▼</text>
            </picker>
          </view>

          <view class="form-item grid-item">
            <text class="label required">车卡</text>
            <picker class="picker" @change="onVehicleCardChange" :value="formData.vehicleCardIndex" :range="vehicleCardOptions" range-key="label">
              <text class="picker-text">{{ formData.vehicleCard || '请选择' }}</text>
              <text class="arrow">▼</text>
            </picker>
          </view>
        </view>

        <view class="form-item">
          <text class="label required">运输起点</text>
          <view class="input-wrapper">
            <input class="input" :value="formData.startPoint" @input="inputChange($event, 'startPoint')" placeholder="请输入运输起点" />
            <view class="input-icon location-start-icon"></view>
          </view>
        </view>

        <view class="form-item">
          <text class="label required">运输终点</text>
          <view class="input-wrapper">
            <input class="input" :value="formData.endPoint" @input="inputChange($event, 'endPoint')" placeholder="请输入运输终点" />
            <view class="input-icon location-end-icon"></view>
          </view>
        </view>

        <view class="form-item">
          <text class="label">是否发生派车信息</text>
          <view class="switch-group">
            <view class="switch-option" :class="{ active: formData.isDispatchInfo === '是' }" @tap="toggleDispatchInfo('是')">是</view>
            <view class="switch-option" :class="{ active: formData.isDispatchInfo === '否' }" @tap="toggleDispatchInfo('否')">否</view>
          </view>
        </view>
      </view>
    </view>

    <view class="form-actions">
      <button class="btn btn-submit" @tap="submitForm">
        <view class="btn-content">
          <text class="btn-text">提交申请</text>
        </view>
      </button>
      <button class="btn btn-cancel" @tap="cancelForm">
        <view class="btn-content">
          <text class="btn-text">取消</text>
        </view>
      </button>
    </view>

      <!-- 固废名称选择弹窗（含遮罩，点击遮罩关闭） -->
      <view v-if="wastePickerVisible" class="picker-modal" @tap="closeWasteNamePicker">
        <view class="picker-panel" @tap.stop>
          <view class="picker-header">
            <text class="picker-title">选择固废名称</text>
            <text class="picker-close" @tap.stop="closeWasteNamePicker">×</text>
          </view>
          <view class="picker-search">
            <input class="search-input" v-model="wasteSearchKeyword" placeholder="搜索固废名称/分类/关键字" @input="onWasteSearch" />
          </view>
          <scroll-view class="picker-list" :scroll-y="true">
            <view v-for="item in filteredWasteList" :key="item.waste_id" class="picker-item" @tap.stop="selectWasteName(item)">
              <text class="picker-item-title">{{ item.waste_name }}</text>
              <text class="picker-item-sub">{{ item.category_code }}</text>
            </view>
            <view v-if="filteredWasteList.length === 0" class="picker-empty">未找到匹配的固废名称</view>
          </scroll-view>
        </view>
      </view>

  </view>
</template>

<script>
import { getTransportFormOptions, getWasteNameList, submitTransfer, getOrderDetail } from '@/services/orderService'
// 完整迁移老版小程序逻辑（使用本地校验与模拟提交）
export default {
  data() {
    return {
      originalOrder: null,
      wastePickerVisible: false,
      wasteList: [],
      filteredWasteList: [],
      wasteSearchKeyword: '',
      selectedWaste: null,
      // 存放从 schema 解析的默认 label，供详情回填时兜底
      packagingDefaultLabel: '',
      transportPurposeDefaultLabel: '',

      formData: {
        // 固废信息
        wasteName: '',
        packagingMethod: '',
        packagingMethodIndex: 0,
        emergencyMeasures: '',
        // 外运信息

        transportPurpose: '',
        transportPurposeIndex: 0,
        wasteDisposalMethod: '',
        wasteDisposalMethodIndex: 0,
        productionEnterprise: '',
        productionEnterpriseId: '',
        productionEnterpriseIndex: 0,
        transportEnterprise: '',
        transportEnterpriseId: '',
        transportEnterpriseIndex: 0,
        receivingUnit: '',
        receivingUnitId: '',
        receivingUnitIndex: 0,
        fiveLinkNumber: '',
        carrier: '',
        transportVehicle: '',
        transportVehicleId: '',
        transportVehicleIndex: 0,
        vehicleCard: '',
        vehicleCardId: '',
        vehicleCardIndex: 0,
        startPoint: '',
        endPoint: '',
        isDispatchInfo: '否',
        isDispatchInfoIndex: 0
      },
      // 选项数据（将由 schema 动态填充）
      packagingOptions: [],
      transportPurposeOptions: [],
      wasteDisposalMethodOptions: [],
      productionEnterpriseOptions: [],
      transportEnterpriseOptions: [],
      receivingUnitOptions: [],
      transportVehicleOptions: [],
      vehicleCardOptions: [],
      yesNoOptions: ['是', '否']
    }
  },

  async onLoad(options) {
    const orderId = options && options.id

    // 1) 从 schema 拉取选项与默认值
    try {
      const opts = await getTransportFormOptions()
      // 下拉选项替换
      this.packagingOptions = opts.packagingOptions.map(o => o.label)
      this.transportPurposeOptions = opts.transportPurposeOptions.map(o => o.label)
      this.wasteDisposalMethodOptions = opts.wasteDisposalMethodOptions.map(o => o.label)
      // 记住默认label，供详情兜底
      this.packagingDefaultLabel = opts.packagingDefault || ''
      this.transportPurposeDefaultLabel = opts.transportPurposeDefault || ''
      this.productionEnterpriseOptions = opts.productionEnterpriseOptions.map(o => ({ label: o.label, value: o.value }))
      this.transportEnterpriseOptions = opts.transportEnterpriseOptions.map(o => ({ label: o.label, value: o.value }))
      this.receivingUnitOptions = opts.receivingUnitOptions.map(o => ({ label: o.label, value: o.value }))
      this.transportVehicleOptions = opts.transportVehicleOptions.map(o => ({ label: o.label, value: o.value }))
      this.vehicleCardOptions = opts.vehicleCardOptions.map(o => ({ label: o.label, value: o.value }))
      // 默认值（如禁忌与应急措施）
      if (opts.emergencyMeasureDefault) {
        this.formData.emergencyMeasures = opts.emergencyMeasureDefault
      }
      // 包装方式默认值
      if (!this.formData.packagingMethod && opts.packagingDefault) {
        const idx = this.packagingOptions.indexOf(opts.packagingDefault)
        if (idx >= 0) {
          this.formData.packagingMethodIndex = idx
          this.formData.packagingMethod = this.packagingOptions[idx]
        }
      }

      // 外运目的默认值
      if (!this.formData.transportPurpose && opts.transportPurposeDefault) {
        const idx = this.transportPurposeOptions.indexOf(opts.transportPurposeDefault)
        if (idx >= 0) {
          this.formData.transportPurposeIndex = idx
          this.formData.transportPurpose = this.transportPurposeOptions[idx]
        }
      }
    } catch (e) {
      console.warn('获取表单选项失败：', e)
    }

    // 2) 详情页传递的订单ID → 回填部分字段（若未从上一页传递，则回退接口查询）
    if (orderId) {
      const passed = (getApp().globalData && getApp().globalData.currentOrderDetail) || null
      if (passed) {
        this.loadOrderDetail(orderId)
      } else {
        try {
          const resp = await getOrderDetail(orderId)
          if (resp && resp.code === 200 && resp.data) {
            getApp().globalData = getApp().globalData || {}
            getApp().globalData.currentOrderDetail = resp.data
            this.loadOrderDetail(orderId)
          }
        } catch (e) {
          console.warn('根据ID获取订单详情失败：', e)
        }
      }
    }

    // 3) 预加载固废名称列表（用于搜索选择器）
    try {
      const list = await getWasteNameList()
      this.wasteList = list
      this.filteredWasteList = list
    } catch (e) {
      console.warn('获取固废名称列表失败：', e)
    }

    // 4) 生成五联单号
    this.generateFiveLinkNumber()
  },

  methods: {
    // 加载订单详情（从列表页传递的数据进行回填）
    loadOrderDetail(orderId) {
      const passed = (getApp().globalData && getApp().globalData.currentOrderDetail) || null
      if (!passed) return

      // 保存订单详情的更多字段，便于提交时直接使用
      this.originalOrder = {
        id: orderId,
        transfer_id: passed.transfer_id || passed.id || orderId,
        apply_code: passed.apply_code || '',
        user_name: passed.user_name || '',
        org_id: passed.org_id || '',
        org_name: passed.org_name || '',
        parent_org_id: passed.parent_org_id || '',
        parent_org_name: passed.parent_org_name || '',
        hwms_org_id: passed.hwms_org_id || '',
        company_id: passed.company_id || '',
        apply_date: passed.apply_date || '',
        is_plan: passed.is_plan || '',
        transfer_type: passed.transfer_type || 'outer',
        is_sales: passed.is_sales || '2',
        phone: passed.phone || '',
        plan_transfer_quantity: passed.plan_transfer_quantity || '',
        plan_transfer_time: passed.plan_transfer_time || '',
        duty_person: passed.duty_person || '',

        // 固废相关
        waste_id: passed.waste_id || '',
        waste_name: passed.waste_name || '',
        category_code: passed.category_code || '',
        parent_category_code: passed.parent_category_code || '',
        report_group_name: passed.report_group_name || '',
        risk: passed.risk || '',
        waste_modal: passed.waste_modal || '',
        harmful_ingredient: passed.harmful_ingredient || '',

        // 页面对应字段
        packagingMethod: passed.package_type || '',
        emergencyMeasures: passed.security_measure || '',
        start: passed.transfer_start_position || '',
        end: passed.transfer_end_position || ''
      }

      // 包装方式回填（优先使用订单详情的 package_type，否则使用 schema 默认）
      const pkgFromOrder = this.originalOrder.packagingMethod
      let packagingIndex = -1
      if (pkgFromOrder) {
        packagingIndex = this.packagingOptions.indexOf(pkgFromOrder)
      }
      if (packagingIndex < 0 && this.packagingDefaultLabel) {
        packagingIndex = this.packagingOptions.indexOf(this.packagingDefaultLabel)
      }
      this.formData.wasteName = this.originalOrder.waste_name || this.originalOrder.wasteName
      if (packagingIndex >= 0) {
        this.formData.packagingMethodIndex = packagingIndex
        this.formData.packagingMethod = this.packagingOptions[packagingIndex]
      }
      this.formData.emergencyMeasures = this.originalOrder.emergencyMeasures || this.formData.emergencyMeasures
      this.formData.startPoint = this.originalOrder.start
      this.formData.endPoint = this.originalOrder.end


      // —— 企业/单位默认值：按ID匹配选中项 ——
      const prodIdx = this.productionEnterpriseOptions.findIndex(o => o.value === passed.produce_enterprise)
      if (prodIdx >= 0) {
        this.formData.productionEnterpriseIndex = prodIdx
        this.formData.productionEnterpriseId = this.productionEnterpriseOptions[prodIdx].value
        this.formData.productionEnterprise = this.productionEnterpriseOptions[prodIdx].label
      }

      const tranIdx = this.transportEnterpriseOptions.findIndex(o => o.value === passed.transfer_enterprise)
      if (tranIdx >= 0) {
        this.formData.transportEnterpriseIndex = tranIdx
        this.formData.transportEnterpriseId = this.transportEnterpriseOptions[tranIdx].value
        this.formData.transportEnterprise = this.transportEnterpriseOptions[tranIdx].label
      }

      const dispIdx = this.receivingUnitOptions.findIndex(o => o.value === passed.dispose_enterprise)
      if (dispIdx >= 0) {
        this.formData.receivingUnitIndex = dispIdx
        this.formData.receivingUnitId = this.receivingUnitOptions[dispIdx].value
        this.formData.receivingUnit = this.receivingUnitOptions[dispIdx].label
      }

      // —— 车辆/车卡默认值：按ID匹配选中项 ——
      const carIdx = this.transportVehicleOptions.findIndex(o => o.value === passed.car_id)
      if (carIdx >= 0) {
        this.formData.transportVehicleIndex = carIdx
        this.formData.transportVehicleId = this.transportVehicleOptions[carIdx].value
        this.formData.transportVehicle = this.transportVehicleOptions[carIdx].label
      }

      const cardIdx = this.vehicleCardOptions.findIndex(o => o.value === passed.card_id)
      if (cardIdx >= 0) {
        this.formData.vehicleCardIndex = cardIdx
        this.formData.vehicleCardId = this.vehicleCardOptions[cardIdx].value
        this.formData.vehicleCard = this.vehicleCardOptions[cardIdx].label
      }

      // —— 其他直接回填 ——
      this.formData.carrier = passed.transfer_person || this.formData.carrier
      this.formData.fiveLinkNumber = passed.five_bills_code || this.formData.fiveLinkNumber
      this.formData.startPoint = passed.transfer_start_position || this.formData.startPoint
      this.formData.endPoint = passed.transfer_end_position || this.formData.endPoint

      // 派车信息标记
      if (passed.is_send_info !== undefined) {
        const yn = passed.is_send_info === '1' ? '是' : '否'
        this.formData.isDispatchInfo = yn
        this.formData.isDispatchInfoIndex = this.yesNoOptions.indexOf(yn)
      }

      // 同步已选固废，便于直接提交
      if (this.originalOrder.waste_id || this.originalOrder.waste_name || this.originalOrder.category_code) {
        this.selectedWaste = {
          waste_id: this.originalOrder.waste_id,
          waste_name: this.originalOrder.waste_name,
          category_code: this.originalOrder.category_code,
          parent_category_code: this.originalOrder.parent_category_code
        }
      }

      // 外运目的默认值（来自 schema.value）
      // 需要将默认值对应到 label 索引
      if (this.transportPurposeOptions && this.transportPurposeOptions.length) {
        // 在 onLoad 中已转为 label 数组，默认值若存在则找到索引
        // 此处仅在传递数据含 outer_goal 时优先
        const tp = passed.outer_goal
        if (tp) {
          const idx = this.transportPurposeOptions.indexOf(tp)
          if (idx >= 0) {
            this.formData.transportPurposeIndex = idx
            this.formData.transportPurpose = this.transportPurposeOptions[idx]
          }
        }
      }

      // 清除全局缓存
      delete getApp().globalData.currentOrderDetail
    },


    // 打开固废名称选择器
    async openWasteNamePicker() {
      if (!this.wasteList || this.wasteList.length === 0) {
        uni.showLoading({ title: '加载中...' })
        try {
          const list = await getWasteNameList()
          this.wasteList = list
          this.filteredWasteList = list
        } catch (e) {
          console.warn('获取固废名称列表失败：', e)
        } finally {
          uni.hideLoading()
        }
      }
      this.wasteSearchKeyword = ''
      this.filteredWasteList = this.wasteList
      this.wastePickerVisible = true
    },

    // 关闭固废名称选择器
    closeWasteNamePicker() {
      this.wastePickerVisible = false
    },

    // 固废名称搜索
    onWasteSearch(e) {
      const kw = (e?.detail?.value ?? this.wasteSearchKeyword ?? '').trim()
      this.wasteSearchKeyword = kw
      if (!kw) {
        this.filteredWasteList = this.wasteList
        return
      }
      const lower = kw.toLowerCase()
      this.filteredWasteList = this.wasteList.filter(it => {
        const name = (it.waste_name || '').toLowerCase()
        const catId = (it.category_id || '').toLowerCase()
        const catName = (it.category_name || '').toLowerCase()
        const catCode = (it.category_code || '').toLowerCase()
        return name.includes(lower) || catId.includes(lower) || catName.includes(lower) || catCode.includes(lower)
      })
    },

    // 选择固废名称
    selectWasteName(item) {
      this.formData.wasteName = item.waste_name
      // 保存选择的固废对象，便于提交取 waste_id/category_code 等
      this.selectedWaste = {
        waste_id: item.waste_id,
        waste_name: item.waste_name,
        category_id: item.category_id,
        category_code: item.category_code,
        parent_category_id: item.parent_category_id
      }
      this.wastePickerVisible = false
    },

    // 生成五联单号
    generateFiveLinkNumber() {
      const date = new Date()
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const random = String(Math.floor(Math.random() * 10000)).padStart(4, '0')
      this.formData.fiveLinkNumber = `FL${year}${month}${day}${random}`
    },

    // 选择器变化
    onPackagingMethodChange(e) {
      const index = e.detail.value
      this.formData.packagingMethodIndex = index
      this.formData.packagingMethod = this.packagingOptions[index]
    },
    onTransportPurposeChange(e) {
      const index = e.detail.value
      this.formData.transportPurposeIndex = index
      this.formData.transportPurpose = this.transportPurposeOptions[index]
    },
    onWasteDisposalMethodChange(e) {
      const index = e.detail.value
      this.formData.wasteDisposalMethodIndex = index
      this.formData.wasteDisposalMethod = this.wasteDisposalMethodOptions[index]
    },
    onProductionEnterpriseChange(e) {
      const index = e.detail.value
      this.formData.productionEnterpriseIndex = index
      const item = this.productionEnterpriseOptions[index]
      this.formData.productionEnterprise = item?.label || ''
      this.formData.productionEnterpriseId = item?.value || ''
    },
    onTransportEnterpriseChange(e) {
      const index = e.detail.value
      this.formData.transportEnterpriseIndex = index
      const item = this.transportEnterpriseOptions[index]
      this.formData.transportEnterprise = item?.label || ''
      this.formData.transportEnterpriseId = item?.value || ''
    },
    onReceivingUnitChange(e) {
      const index = e.detail.value
      this.formData.receivingUnitIndex = index
      const item = this.receivingUnitOptions[index]
      this.formData.receivingUnit = item?.label || ''
      this.formData.receivingUnitId = item?.value || ''
    },
    onTransportVehicleChange(e) {
      const index = e.detail.value
      this.formData.transportVehicleIndex = index
      const item = this.transportVehicleOptions[index]
      this.formData.transportVehicle = item?.label || ''
      this.formData.transportVehicleId = item?.value || ''
    },
    onVehicleCardChange(e) {
      const index = e.detail.value
      this.formData.vehicleCardIndex = index
      const item = this.vehicleCardOptions[index]
      this.formData.vehicleCard = item?.label || ''
      this.formData.vehicleCardId = item?.value || ''
    },

    // 是否发生派车信息
    toggleDispatchInfo(value) {
      const index = this.yesNoOptions.indexOf(value)
      this.formData.isDispatchInfo = value
      this.formData.isDispatchInfoIndex = index
    },

    // 输入框变化
    inputChange(e, field) {
      const value = e.detail.value
      this.$set(this.formData, field, value)
    },

    // 校验与提交（真实 PUT 提交）
    async submitForm() {
      const f = this.formData
      if (!f.wasteName) {
        return uni.showToast({ title: '请填写固废名称', icon: 'none' })
      }
      if (!f.packagingMethod) {
        return uni.showToast({ title: '请选择包装方式', icon: 'none' })
      }
      if (!f.transportPurpose) {
        return uni.showToast({ title: '请选择外运目的', icon: 'none' })
      }
      if (!f.transportVehicle) {
        return uni.showToast({ title: '请选择运输车牌', icon: 'none' })
      }
      if (!f.vehicleCard) {
        return uni.showToast({ title: '请选择车卡', icon: 'none' })
      }
      if (!f.startPoint || !f.endPoint) {
        return uni.showToast({ title: '请填写运输起止地点', icon: 'none' })
      }

      try {
        uni.showLoading({ title: '提交中...' })

        // 构造提交体（示例中部分字段映射自页面/默认值或回填数据）
        const payload = {
          transfer_id: this.originalOrder?.transfer_id || this.originalOrder?.id || '',
          apply_code: this.originalOrder?.apply_code || f.fiveLinkNumber, // 若无apply_code就用五联单号
          user_name: this.originalOrder?.user_name || '',
          org_id: this.originalOrder?.org_id || '',
          org_name: this.originalOrder?.org_name || '',
          parent_org_id: this.originalOrder?.parent_org_id || '',
          parent_org_name: this.originalOrder?.parent_org_name || '',
          hwms_org_id: this.originalOrder?.hwms_org_id || '',
          company_id: this.originalOrder?.company_id || '',
          apply_date: this.originalOrder?.apply_date || Date.now(),
          apply_type: 'station',
          is_plan: this.originalOrder?.is_plan || '',
          transfer_type: 'outer',
          is_sales: this.originalOrder?.is_sales || '2',
          phone: this.originalOrder?.phone || '',
          plan_transfer_quantity: this.originalOrder?.plan_transfer_quantity || '1',
          plan_transfer_time: this.originalOrder?.plan_transfer_time || Date.now(),
          duty_person: this.originalOrder?.duty_person || '',
          // 固废相关（选择器）
          waste_id: this.selectedWaste?.waste_id || '',
          waste_name: f.wasteName,
          category_code: this.selectedWaste?.category_code || '',
          parent_category_code: this.originalOrder?.parent_category_code || '',
          report_group_name: this.originalOrder?.report_group_name || '',
          risk: this.originalOrder?.risk || '',
          waste_modal: this.originalOrder?.waste_modal || '',
          harmful_ingredient: this.originalOrder?.harmful_ingredient || '',
          // 页面选项
          package_type: f.packagingMethod,
          security_measure: f.emergencyMeasures,
          outer_goal: f.transportPurpose,
          // 设备/车辆
          transfer_person: f.carrier,
          car_id: f.transportVehicleId,
          card_id: f.vehicleCardId,
          // 路线
          transfer_start_position: f.startPoint,
          transfer_end_position: f.endPoint,
          // 组织
          produce_enterprise: f.productionEnterpriseId,
          transfer_enterprise: f.transportEnterpriseId,
          dispose_enterprise: f.receivingUnitId,

          // 其他字段（保留示例字段，后台可能忽略/或需要）
          five_bills_code: f.fiveLinkNumber,
          note: this.originalOrder?.note || '',
          bpm_status: this.originalOrder?.bpm_status || 'finish',
          bpm_update_time: this.originalOrder?.bpm_update_time || Date.now(),
          update_time: Date.now(),
          is_send_info: this.originalOrder?.is_send_info || '0',
          year: String(new Date().getFullYear()),
          division_id: this.originalOrder?.division_id || this.originalOrder?.company_id || '',
          handle_type: '20'
        }

        // transferId 优先取 originalOrder.transfer_id 或路由参数
        const transferId = this.originalOrder?.transfer_id || this.originalOrder?.id
        if (!transferId) {
          uni.hideLoading()
          return uni.showToast({ title: '缺少订单ID，无法提交', icon: 'none' })
        }

        await submitTransfer(transferId, payload)

        uni.hideLoading()
        uni.showToast({ title: '提交成功', icon: 'success' })
        setTimeout(() => {
          uni.navigateBack()
        }, 1000)
      } catch (err) {
        uni.hideLoading()
        const msg = err?.content || err?.message || '提交失败'
        uni.showToast({ title: msg, icon: 'none' })
        console.warn('提交失败：', err)
      }
    },

    // 取消
    cancelForm() {
      uni.showModal({
        title: '取消确认',
        content: '确定要取消填写吗？已输入的数据将不会保存。',
        success: (res) => {
          if (res.confirm) {
            uni.navigateBack({
              delta: 1,
              fail: () => {
                uni.redirectTo({ url: '/pages/orderList/orderList' })
              }
            })
          }
        }
      })
    }
  }
}
</script>

<style scoped>
/* 联系外运填写信息页面样式 */
.container {
  padding: 30rpx 20rpx;
  background-color: #f7f7f7;
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  margin-bottom: 40rpx;
  background-color: #ffffff;
  padding: 30rpx 30rpx;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.page-title {
  font-size: 38rpx;
  font-weight: bold;
  color: #333333;
  display: block;
  margin-bottom: 12rpx;
  position: relative;
  padding-left: 20rpx;
}

.page-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 10rpx;
  height: 36rpx;
  width: 8rpx;
  background: linear-gradient(to bottom, #1976D2, #64B5F6);
  border-radius: 4rpx;
}

.page-subtitle {
  font-size: 24rpx;
  color: #666666;
  display: block;
  padding-left: 20rpx;
}

/* 表单部分 */
.form-section {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  padding: 0 0 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 30rpx;
  position: relative;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  display: block;
  width: 16rpx;
  height: 16rpx;
  background-color: #1976D2;
  border-radius: 50%;
  margin-right: 16rpx;
}

.title-text { font-weight: 500; }
.form-item { margin-bottom: 30rpx; }

.label {
  display: block;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 14rpx;
  font-weight: 500;
}

.label.required::after { content: '*'; color: #ff4d4f; margin-left: 8rpx; font-weight: bold; }

/* 表单网格布局 */
.form-grid { display: flex; flex-wrap: wrap; margin: 0 -10rpx; }
.grid-item { flex: 1; min-width: 45%; padding: 0 10rpx; }

/* 输入框包装器 */
.input-wrapper { position: relative; width: 100%; display: flex; align-items: center; }
.input {
  width: 100%; height: 88rpx; border: 1rpx solid #e0e0e0; border-radius: 44rpx; background-color: #ffffff;
  padding: 0 30rpx; font-size: 28rpx; box-sizing: border-box; color: #333333; transition: all 0.3s ease;
  box-shadow: inset 0 2rpx 6rpx rgba(0, 0, 0, 0.05); display: flex; align-items: center; line-height: 88rpx;
}
.input::placeholder { color: #999999; line-height: 88rpx; vertical-align: middle; }
.input:focus { border-color: #1976D2; box-shadow: 0 0 0 2rpx rgba(25, 118, 210, 0.2); }

.input-icon { position: absolute; right: 30rpx; top: 50%; transform: translateY(-50%); width: 40rpx; height: 40rpx; opacity: 0.5; }
.waste-icon { background-size: cover; }
.person-icon { background-size: cover; }
.location-start-icon { background-size: cover; }
.location-end-icon { background-size: cover; }

/* 文本区域 */
.textarea-wrapper { position: relative; width: 100%; }
.textarea {
  width: 100%; height: 180rpx; border: 1rpx solid #e0e0e0; border-radius: 16rpx; background-color: #ffffff;
  padding: 20rpx 30rpx; font-size: 28rpx; box-sizing: border-box; color: #333333; transition: all 0.3s ease;
  box-shadow: inset 0 2rpx 6rpx rgba(0, 0, 0, 0.05); line-height: 1.5;
}
.textarea::placeholder { color: #999999; vertical-align: middle; }
.textarea:focus { border-color: #1976D2; box-shadow: 0 0 0 2rpx rgba(25, 118, 210, 0.2); }

/* 选择器 */
.picker {
  width: 100%; height: 88rpx; border: 1rpx solid #e0e0e0; border-radius: 44rpx; background-color: #ffffff;
  padding: 0 30rpx; box-sizing: border-box; box-shadow: inset 0 2rpx 6rpx rgba(0, 0, 0, 0.05); position: relative;
}
.picker-text { display: block; width: calc(100% - 40rpx); color: #333333; font-size: 28rpx; line-height: 88rpx; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }
.arrow { position: absolute; right: 30rpx; top: 50%; transform: translateY(-50%); color: #1976D2; font-size: 22rpx; }

/* 特殊输入框 - 自动生成标签 */
.special-input { position: relative; }
.auto-generated-tag {
  position: absolute; right: 20rpx; top: 50%; transform: translateY(-50%);

/* 选择器样式（对齐 orderList 的公司选择外观） */
.selector { height: 72rpx; padding: 0 24rpx; background: #f5f6f7; border-radius: 8rpx; display: flex; align-items: center; justify-content: space-between; }
.selector-text { font-size: 28rpx; color: #333; flex: 1; }
.dropdown-arrow { font-size: 22rpx; color: #666; transition: transform 0.3s; }
.dropdown-arrow-up { transform: rotate(180deg); }

  background-color: #E3F2FD; color: #1976D2; padding: 4rpx 12rpx; border-radius: 16rpx; font-size: 20rpx; font-weight: 500;
}

/* 切换开关组样式 */
.switch-group {
  display: flex; width: 100%; border-radius: 44rpx; border: 1rpx solid #e0e0e0; overflow: hidden; height: 88rpx;
  box-shadow: inset 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}
.switch-option { flex: 1; height: 100%; line-height: 88rpx; text-align: center; font-size: 28rpx; color: #666666; background-color: #f9f9f9; transition: all 0.3s ease; }
.switch-option.active { color: #ffffff; background-color: #1976D2; font-weight: 500; }

/* 分隔线 */
.divider { height: 1rpx; background-color: #f0f0f0; margin: 30rpx 0; }

/* 按钮 */
.form-actions { display: flex; justify-content: space-between; padding: 40rpx 0 80rpx; }
.btn { flex: 1; height: 90rpx; line-height: 90rpx; text-align: center; border-radius: 45rpx; font-size: 32rpx; font-weight: 500; letter-spacing: 2rpx; transition: all 0.3s ease; position: relative; overflow: hidden; }
.btn::after { content: ""; display: block; position: absolute; width: 100%; height: 100%; top: 0; left: 0; pointer-events: none; background-image: radial-gradient(circle, #fff 10%, transparent 10.01%); background-repeat: no-repeat; background-position: 50%; transform: scale(10, 10); opacity: 0; transition: transform 0.5s, opacity 1s; }
.btn:active::after { transform: scale(0, 0); opacity: 0.3; transition: 0s; }
.btn-content { display: flex; align-items: center; justify-content: center; height: 100%; }
.btn-text { font-size: 32rpx; }
.btn-submit { margin-right: 20rpx; background: linear-gradient(135deg, #42A5F5, #1976D2); color: #ffffff; border: none; box-shadow: 0 8rpx 20rpx rgba(25, 118, 210, 0.3); }
.btn-submit:active { transform: translateY(2rpx); box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.3); }
.btn-cancel { margin-left: 20rpx; background-color: #ffffff; color: #666666; border: 1rpx solid #e0e0e0; box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08); display: flex; align-items: center; justify-content: center; }
.btn-cancel:active { transform: translateY(2rpx); box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08); }

/* 表单项禁用样式 */

/* 固废名称选择弹窗样式（参考列表页筛选弹窗的层级与布局） */
.picker-modal {
  position: fixed;
  left: 0; right: 0; top: 0; bottom: 0;
  background: rgba(0,0,0,0.35);
  z-index: 99999;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}
.picker-panel {
  width: 100%;
  background: #fff;
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}
.picker-header { display: flex; align-items: center; justify-content: space-between; padding: 20rpx 24rpx; border-bottom: 1rpx solid #f0f0f0; }
.picker-title { font-size: 30rpx; font-weight: 600; color: #333; }
.picker-close { font-size: 36rpx; color: #999; padding: 10rpx; }
.picker-search { padding: 16rpx 24rpx; }

/* 弹层滚动区域必须可滚动（解决H5端不滚问题） */
.picker-list {
  max-height: 60vh;
  overflow: auto;
}

.search-input {
  width: 100%; height: 72rpx; border: 1rpx solid #e0e0e0; border-radius: 36rpx; padding: 0 24rpx; box-sizing: border-box; font-size: 28rpx;
}
.picker-list { flex: 1; }
.picker-item { padding: 20rpx 24rpx; border-bottom: 1rpx solid #f5f5f5; }
.picker-item-title { display: block; font-size: 30rpx; color: #333; line-height: 1.4; }
.picker-item-sub { display: block; font-size: 24rpx; color: #999; margin-top: 6rpx; line-height: 1.4; }
.picker-empty { text-align: center; color: #999; padding: 40rpx 0; }

/* H5 端点击容器可见反馈与指针 */
.selector { cursor: pointer; }

input[disabled] { background-color: #f9f9f9; color: #666666; box-shadow: none; }
</style>

/* 固废名称选择弹窗样式（参考列表页筛选弹窗的层级与布局） */
.picker-modal {
  position: fixed;
  left: 0; right: 0; top: 0; bottom: 0;
  background: rgba(0,0,0,0.35);
  z-index: 99999;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}
.picker-panel {
  width: 100%;
  background: #fff;
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}
.picker-header { display: flex; align-items: center; justify-content: space-between; padding: 20rpx 24rpx; border-bottom: 1rpx solid #f0f0f0; }
.picker-title { font-size: 30rpx; font-weight: 600; color: #333; }
.picker-close { font-size: 36rpx; color: #999; padding: 10rpx; }
.picker-search { padding: 16rpx 24rpx; }
.search-input {
  width: 100%; height: 72rpx; border: 1rpx solid #e0e0e0; border-radius: 36rpx; padding: 0 24rpx; box-sizing: border-box; font-size: 28rpx;
}
.picker-list { flex: 1; }
.picker-item { padding: 20rpx 24rpx; border-bottom: 1rpx solid #f5f5f5; }
.picker-item-title { font-size: 30rpx; color: #333; }
.picker-item-sub { font-size: 24rpx; color: #999; margin-top: 6rpx; }
.picker-empty { text-align: center; color: #999; padding: 40rpx 0; }
